#!/usr/bin/env python3
"""
K线箱体检测结果非极大值抑制（NMS）后处理程序

功能：
1. 对箱体检测结果进行NMS处理
2. 计算箱体相关的技术分析指标
3. 输出处理后的箱体数据

作者：AI Assistant
"""

import json
import csv
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import argparse
from pathlib import Path


@dataclass
class BoxCandidate:
    """箱体候选类"""
    left_idx: int      # 左边界K线索引
    right_idx: int     # 右边界K线索引
    score: float       # 箱体得分
    min_price: float = 0.0   # 箱体最低价
    max_price: float = 0.0   # 箱体最高价
    
    def duration(self) -> int:
        """箱体持续时间（K线数量）"""
        return self.right_idx - self.left_idx + 1
    
    def price_range(self) -> float:
        """箱体价格范围"""
        return self.max_price - self.min_price
    
    def area(self) -> float:
        """箱体面积（时间 × 价格范围）"""
        return self.duration() * self.price_range()


class BoxNMSProcessor:
    """箱体NMS处理器"""
    
    def __init__(self, iou_threshold: float = 0.5, min_score: float = 0.0):
        """
        初始化处理器
        
        Args:
            iou_threshold: IoU阈值，超过此值的箱体将被抑制
            min_score: 最小得分阈值，低于此值的箱体将被过滤
        """
        self.iou_threshold = iou_threshold
        self.min_score = min_score
    
    def load_kline_data(self, json_path: str) -> List[Dict]:
        """加载K线数据"""
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convert to structured format
        klines = []
        for item in data:
            klines.append({
                'timestamp': int(item[0]),
                'open': float(item[1]),
                'high': float(item[2]),
                'low': float(item[3]),
                'close': float(item[4]),
                'volume': float(item[5])
            })
        return klines
    
    def load_scores_matrix(self, csv_path: str) -> np.ndarray:
        """加载得分矩阵"""
        scores = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                # Convert empty strings to 0, others to float
                score_row = []
                for cell in row:
                    if cell.strip() == '':
                        score_row.append(0.0)
                    else:
                        try:
                            score_row.append(float(cell))
                        except ValueError:
                            score_row.append(0.0)
                scores.append(score_row)
        return np.array(scores)
    
    def extract_candidates(self, scores_matrix: np.ndarray) -> List[BoxCandidate]:
        """从得分矩阵提取候选箱体"""
        candidates = []
        rows, cols = scores_matrix.shape
        
        for i in range(rows):
            for j in range(cols):
                score = scores_matrix[i, j]
                if score > self.min_score:
                    candidates.append(BoxCandidate(
                        left_idx=i,
                        right_idx=j,
                        score=score
                    ))
        
        return candidates
    
    def calculate_box_bounds(self, candidate: BoxCandidate, klines: List[Dict]) -> BoxCandidate:
        """计算箱体的价格边界"""
        if candidate.right_idx >= len(klines) or candidate.left_idx < 0:
            return candidate
        
        # Extract price data for the box duration
        box_klines = klines[candidate.left_idx:candidate.right_idx + 1]
        
        if not box_klines:
            return candidate
        
        # Calculate min and max prices
        highs = [k['high'] for k in box_klines]
        lows = [k['low'] for k in box_klines]
        
        candidate.min_price = min(lows)
        candidate.max_price = max(highs)
        
        return candidate
    
    def calculate_iou(self, box1: BoxCandidate, box2: BoxCandidate) -> float:
        """
        计算两个箱体的IoU（交并比）
        
        考虑时间和价格两个维度的重叠
        """
        # Time dimension overlap
        time_overlap = max(0, min(box1.right_idx, box2.right_idx) - max(box1.left_idx, box2.left_idx) + 1)
        time_union = max(box1.right_idx, box2.right_idx) - min(box1.left_idx, box2.left_idx) + 1
        
        # Price dimension overlap
        price_overlap = max(0, min(box1.max_price, box2.max_price) - max(box1.min_price, box2.min_price))
        price_union = max(box1.max_price, box2.max_price) - min(box1.min_price, box2.min_price)
        
        # Calculate 2D IoU
        if time_union == 0 or price_union == 0:
            return 0.0
        
        intersection = time_overlap * price_overlap
        union = (box1.area() + box2.area()) - intersection
        
        if union == 0:
            return 0.0
        
        return intersection / union
    
    def is_contained(self, box1: BoxCandidate, box2: BoxCandidate) -> bool:
        """
        检查box1是否完全包含在box2中
        """
        time_contained = (box1.left_idx >= box2.left_idx and box1.right_idx <= box2.right_idx)
        price_contained = (box1.min_price >= box2.min_price and box1.max_price <= box2.max_price)
        
        return time_contained and price_contained
    
    def nms(self, candidates: List[BoxCandidate]) -> List[BoxCandidate]:
        """
        执行非极大值抑制
        
        特殊处理：如果一个大箱体完全包含一个小箱体，不进行抑制
        """
        if not candidates:
            return []
        
        # Sort by score in descending order
        candidates = sorted(candidates, key=lambda x: x.score, reverse=True)
        
        selected = []
        
        for candidate in candidates:
            should_keep = True
            
            for selected_box in selected:
                iou = self.calculate_iou(candidate, selected_box)
                
                # Check if one box completely contains the other
                candidate_contains_selected = self.is_contained(selected_box, candidate)
                selected_contains_candidate = self.is_contained(candidate, selected_box)
                
                # If there's significant overlap but no complete containment, suppress
                if iou > self.iou_threshold and not (candidate_contains_selected or selected_contains_candidate):
                    should_keep = False
                    break
            
            if should_keep:
                selected.append(candidate)
        
        return selected

    def calculate_technical_indicators(self, box: BoxCandidate, klines: List[Dict]) -> Dict:
        """
        计算箱体的技术分析指标
        """
        if box.right_idx >= len(klines) or box.left_idx < 0:
            return {}

        # Extract box data
        box_klines = klines[box.left_idx:box.right_idx + 1]

        if not box_klines:
            return {}

        # Basic statistics
        opens = [k['open'] for k in box_klines]
        highs = [k['high'] for k in box_klines]
        lows = [k['low'] for k in box_klines]
        closes = [k['close'] for k in box_klines]
        volumes = [k['volume'] for k in box_klines]

        # Price statistics
        price_range = box.price_range()
        price_center = (box.max_price + box.min_price) / 2

        # Volume statistics
        total_volume = sum(volumes)
        avg_volume = total_volume / len(volumes) if volumes else 0
        volume_std = np.std(volumes) if len(volumes) > 1 else 0

        # Price volatility (standard deviation of close prices)
        price_volatility = np.std(closes) if len(closes) > 1 else 0

        # Price change within box
        price_change = closes[-1] - closes[0] if len(closes) >= 2 else 0
        price_change_pct = (price_change / closes[0] * 100) if closes[0] != 0 else 0

        # Box strength indicators
        touches_upper = sum(1 for h in highs if abs(h - box.max_price) / box.max_price < 0.01)
        touches_lower = sum(1 for l in lows if abs(l - box.min_price) / box.min_price < 0.01)

        # Time-based indicators
        duration_hours = box.duration() * 4  # Assuming 4-hour candles

        return {
            'basic_stats': {
                'duration_candles': box.duration(),
                'duration_hours': duration_hours,
                'price_range': price_range,
                'price_center': price_center,
                'area': box.area()
            },
            'price_stats': {
                'min_price': box.min_price,
                'max_price': box.max_price,
                'open_price': opens[0] if opens else 0,
                'close_price': closes[-1] if closes else 0,
                'price_change': price_change,
                'price_change_pct': price_change_pct,
                'price_volatility': price_volatility
            },
            'volume_stats': {
                'total_volume': total_volume,
                'avg_volume': avg_volume,
                'volume_std': volume_std,
                'max_volume': max(volumes) if volumes else 0,
                'min_volume': min(volumes) if volumes else 0
            },
            'strength_indicators': {
                'upper_touches': touches_upper,
                'lower_touches': touches_lower,
                'touch_ratio': (touches_upper + touches_lower) / len(box_klines) if box_klines else 0
            }
        }

    def process(self, scores_csv_path: str, klines_json_path: str, output_path: str) -> Dict:
        """
        执行完整的处理流程
        """
        print("Loading data...")
        klines = self.load_kline_data(klines_json_path)
        scores_matrix = self.load_scores_matrix(scores_csv_path)

        print(f"Loaded {len(klines)} K-lines and {scores_matrix.shape} scores matrix")

        print("Extracting candidates...")
        candidates = self.extract_candidates(scores_matrix)
        print(f"Found {len(candidates)} initial candidates")

        print("Calculating box bounds...")
        for candidate in candidates:
            self.calculate_box_bounds(candidate, klines)

        print("Applying NMS...")
        selected_boxes = self.nms(candidates)
        print(f"Selected {len(selected_boxes)} boxes after NMS")

        print("Calculating technical indicators...")
        results = []
        for i, box in enumerate(selected_boxes):
            indicators = self.calculate_technical_indicators(box, klines)

            result = {
                'box_id': i,
                'boundaries': {
                    'left_idx': box.left_idx,
                    'right_idx': box.right_idx,
                    'min_price': box.min_price,
                    'max_price': box.max_price
                },
                'score': box.score,
                'technical_indicators': indicators
            }
            results.append(result)

        # Prepare output
        output_data = {
            'metadata': {
                'total_candidates': len(candidates),
                'selected_boxes': len(selected_boxes),
                'iou_threshold': self.iou_threshold,
                'min_score': self.min_score,
                'klines_count': len(klines)
            },
            'boxes': results
        }

        # Save results
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)

        print(f"Results saved to {output_path}")
        return output_data


def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='K线箱体检测结果NMS后处理')
    parser.add_argument('--scores', required=True, help='得分CSV文件路径')
    parser.add_argument('--klines', required=True, help='K线JSON文件路径')
    parser.add_argument('--output', required=True, help='输出JSON文件路径')
    parser.add_argument('--iou-threshold', type=float, default=0.5, help='IoU阈值 (默认: 0.5)')
    parser.add_argument('--min-score', type=float, default=0.0, help='最小得分阈值 (默认: 0.0)')

    args = parser.parse_args()

    # Validate input files
    if not Path(args.scores).exists():
        print(f"Error: Scores file {args.scores} not found")
        return

    if not Path(args.klines).exists():
        print(f"Error: K-lines file {args.klines} not found")
        return

    # Create processor
    processor = BoxNMSProcessor(
        iou_threshold=args.iou_threshold,
        min_score=args.min_score
    )

    # Process
    try:
        results = processor.process(args.scores, args.klines, args.output)
        print(f"\nProcessing completed successfully!")
        print(f"Total boxes found: {results['metadata']['selected_boxes']}")
    except Exception as e:
        print(f"Error during processing: {e}")
        raise


if __name__ == "__main__":
    main()
