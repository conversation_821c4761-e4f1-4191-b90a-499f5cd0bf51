#!/usr/bin/env python3
"""
K线箱体检测结果的非极大值抑制（NMS）后处理程序

功能：
1. 对箱体检测结果进行NMS处理
2. 计算箱体相关的技术分析指标
3. 输出处理后的箱体数据

作者：AI Assistant
"""

import json
import csv
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import argparse
from pathlib import Path


@dataclass
class Box:
    """箱体数据结构"""
    left_idx: int      # 左边界K线索引
    right_idx: int     # 右边界K线索引
    score: float       # 箱体得分
    high_price: float  # 箱体上边界价格
    low_price: float   # 箱体下边界价格
    
    # Technical indicators will be added later
    volume_total: Optional[float] = None
    volume_avg: Optional[float] = None
    price_range: Optional[float] = None
    volatility: Optional[float] = None
    duration_hours: Optional[int] = None
    kline_count: Optional[int] = None


class BoxNMSProcessor:
    """箱体NMS处理器"""
    
    def __init__(self, iou_threshold: float = 0.5, score_threshold: float = 0.0):
        """
        初始化NMS处理器
        
        Args:
            iou_threshold: IoU阈值，超过此值认为重叠
            score_threshold: 得分阈值，低于此值的箱体将被过滤
        """
        self.iou_threshold = iou_threshold
        self.score_threshold = score_threshold
        
    def load_kline_data(self, kline_file: str) -> List[Dict]:
        """加载K线数据"""
        with open(kline_file, 'r') as f:
            kline_data = json.load(f)
        
        # Convert to structured format
        structured_data = []
        for kline in kline_data:
            structured_data.append({
                'timestamp': int(kline[0]),
                'open': float(kline[1]),
                'high': float(kline[2]),
                'low': float(kline[3]),
                'close': float(kline[4]),
                'volume': float(kline[5]),
                'close_time': int(kline[6]),
                'quote_volume': float(kline[7]),
                'trades': int(kline[8]),
                'taker_buy_volume': float(kline[9]),
                'taker_buy_quote_volume': float(kline[10])
            })
        
        return structured_data
    
    def load_scores(self, scores_file: str) -> List[Box]:
        """从scores.csv加载候选箱体"""
        boxes = []
        
        with open(scores_file, 'r') as f:
            reader = csv.reader(f)
            for row_idx, row in enumerate(reader):
                for col_idx, score_str in enumerate(row):
                    if score_str and score_str.strip():
                        try:
                            score = float(score_str)
                            if score > self.score_threshold:
                                # Create box with placeholder price boundaries
                                box = Box(
                                    left_idx=row_idx,
                                    right_idx=col_idx,
                                    score=score,
                                    high_price=0.0,  # Will be calculated later
                                    low_price=0.0    # Will be calculated later
                                )
                                boxes.append(box)
                        except ValueError:
                            continue
        
        return boxes
    
    def calculate_box_boundaries(self, boxes: List[Box], kline_data: List[Dict]) -> List[Box]:
        """计算箱体的价格边界"""
        for box in boxes:
            if box.right_idx >= len(kline_data) or box.left_idx >= len(kline_data):
                continue
                
            # Get K-lines within the box time range
            box_klines = kline_data[box.left_idx:box.right_idx + 1]
            
            if not box_klines:
                continue
                
            # Calculate price boundaries
            highs = [k['high'] for k in box_klines]
            lows = [k['low'] for k in box_klines]
            
            box.high_price = max(highs)
            box.low_price = min(lows)
        
        return boxes
    
    def calculate_iou(self, box1: Box, box2: Box) -> float:
        """
        计算两个箱体的IoU (Intersection over Union)
        
        考虑时间和价格两个维度的重叠
        """
        # Time dimension intersection
        time_left = max(box1.left_idx, box2.left_idx)
        time_right = min(box1.right_idx, box2.right_idx)
        time_intersection = max(0, time_right - time_left + 1)
        
        # Price dimension intersection  
        price_top = min(box1.high_price, box2.high_price)
        price_bottom = max(box1.low_price, box2.low_price)
        price_intersection = max(0, price_top - price_bottom)
        
        # Calculate intersection area
        intersection_area = time_intersection * price_intersection
        
        # Calculate union area
        box1_time_span = box1.right_idx - box1.left_idx + 1
        box1_price_span = box1.high_price - box1.low_price
        box1_area = box1_time_span * box1_price_span
        
        box2_time_span = box2.right_idx - box2.left_idx + 1
        box2_price_span = box2.high_price - box2.low_price
        box2_area = box2_time_span * box2_price_span
        
        union_area = box1_area + box2_area - intersection_area
        
        # Avoid division by zero
        if union_area == 0:
            return 0.0
            
        return intersection_area / union_area
    
    def is_contained(self, box1: Box, box2: Box) -> bool:
        """
        检查box1是否完全包含在box2中，或box2是否完全包含在box1中
        
        如果存在包含关系，则不应该进行NMS抑制
        """
        # Check if box1 contains box2
        box1_contains_box2 = (
            box1.left_idx <= box2.left_idx and 
            box1.right_idx >= box2.right_idx and
            box1.low_price <= box2.low_price and 
            box1.high_price >= box2.high_price
        )
        
        # Check if box2 contains box1
        box2_contains_box1 = (
            box2.left_idx <= box1.left_idx and 
            box2.right_idx >= box1.right_idx and
            box2.low_price <= box1.low_price and 
            box2.high_price >= box1.high_price
        )
        
        return box1_contains_box2 or box2_contains_box1
