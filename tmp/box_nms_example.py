#!/usr/bin/env python3
"""
K线箱体NMS处理器使用示例

这个示例展示了如何使用BoxNMSProcessor来处理箱体检测结果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from box_nms_processor import BoxNMSProcessor


def example_usage():
    """使用示例"""
    
    # 创建处理器实例
    processor = BoxNMSProcessor(
        iou_threshold=0.3,  # IoU阈值，可以根据需要调整
        min_score=10.0      # 最小得分阈值，过滤低分候选
    )
    
    # 输入文件路径
    scores_file = "scores.csv"
    klines_file = "BTCUSDT_4h.json"
    output_file = "tmp/processed_boxes.json"
    
    # 执行处理
    try:
        results = processor.process(scores_file, klines_file, output_file)
        
        # 打印处理结果摘要
        print("\n=== 处理结果摘要 ===")
        print(f"原始候选箱体数量: {results['metadata']['total_candidates']}")
        print(f"NMS后箱体数量: {results['metadata']['selected_boxes']}")
        print(f"IoU阈值: {results['metadata']['iou_threshold']}")
        print(f"最小得分阈值: {results['metadata']['min_score']}")
        
        # 显示前几个箱体的详细信息
        print("\n=== 前5个箱体详情 ===")
        for i, box in enumerate(results['boxes'][:5]):
            print(f"\n箱体 {i+1}:")
            print(f"  时间范围: K线 {box['boundaries']['left_idx']} - {box['boundaries']['right_idx']}")
            print(f"  价格范围: {box['boundaries']['min_price']:.2f} - {box['boundaries']['max_price']:.2f}")
            print(f"  得分: {box['score']:.2f}")
            
            # 技术指标
            indicators = box['technical_indicators']
            if 'basic_stats' in indicators:
                basic = indicators['basic_stats']
                print(f"  持续时间: {basic['duration_candles']} 根K线 ({basic['duration_hours']} 小时)")
                print(f"  价格范围: {basic['price_range']:.2f}")
                print(f"  箱体面积: {basic['area']:.2f}")
            
            if 'volume_stats' in indicators:
                volume = indicators['volume_stats']
                print(f"  总成交量: {volume['total_volume']:.2f}")
                print(f"  平均成交量: {volume['avg_volume']:.2f}")
            
            if 'strength_indicators' in indicators:
                strength = indicators['strength_indicators']
                print(f"  上边界触碰次数: {strength['upper_touches']}")
                print(f"  下边界触碰次数: {strength['lower_touches']}")
                print(f"  触碰比率: {strength['touch_ratio']:.2%}")
        
        print(f"\n完整结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return False
    
    return True


def analyze_results(results_file: str):
    """分析处理结果"""
    import json
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        boxes = data['boxes']
        
        print("\n=== 箱体统计分析 ===")
        
        # 得分分布
        scores = [box['score'] for box in boxes]
        print(f"得分统计:")
        print(f"  最高得分: {max(scores):.2f}")
        print(f"  最低得分: {min(scores):.2f}")
        print(f"  平均得分: {sum(scores)/len(scores):.2f}")
        
        # 持续时间分布
        durations = [box['technical_indicators']['basic_stats']['duration_candles'] for box in boxes]
        print(f"\n持续时间统计:")
        print(f"  最长持续: {max(durations)} 根K线")
        print(f"  最短持续: {min(durations)} 根K线")
        print(f"  平均持续: {sum(durations)/len(durations):.1f} 根K线")
        
        # 价格范围分布
        price_ranges = [box['technical_indicators']['basic_stats']['price_range'] for box in boxes]
        print(f"\n价格范围统计:")
        print(f"  最大范围: {max(price_ranges):.2f}")
        print(f"  最小范围: {min(price_ranges):.2f}")
        print(f"  平均范围: {sum(price_ranges)/len(price_ranges):.2f}")
        
        # 强度指标统计
        touch_ratios = [box['technical_indicators']['strength_indicators']['touch_ratio'] for box in boxes]
        print(f"\n强度指标统计:")
        print(f"  最高触碰比率: {max(touch_ratios):.2%}")
        print(f"  最低触碰比率: {min(touch_ratios):.2%}")
        print(f"  平均触碰比率: {sum(touch_ratios)/len(touch_ratios):.2%}")
        
    except Exception as e:
        print(f"分析结果时出现错误: {e}")


if __name__ == "__main__":
    print("K线箱体NMS处理器使用示例")
    print("=" * 50)
    
    # 执行处理示例
    success = example_usage()
    
    if success:
        # 分析结果
        analyze_results("tmp/processed_boxes.json")
    
    print("\n示例执行完成！")
