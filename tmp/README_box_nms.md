# K线箱体检测结果NMS后处理程序

## 概述

这个程序实现了对K线箱体检测结果的非极大值抑制（NMS）处理，能够筛选出合理且不重叠的候选箱体，并为每个箱体计算相关的技术分析指标。

## 主要功能

### 1. 非极大值抑制（NMS）算法
- 基于IoU（交并比）计算箱体重叠度
- 智能处理包含关系：大箱体完全包含小箱体时不视为重叠
- 可配置的IoU阈值参数

### 2. 箱体数据增强
为每个筛选出的箱体计算以下技术指标：

#### 基础统计指标
- 持续时间（K线数量和小时数）
- 价格范围（最高价-最低价）
- 价格中心点
- 箱体面积（时间×价格范围）

#### 价格统计指标
- 开盘价、收盘价、最高价、最低价
- 价格变化和变化百分比
- 价格波动率（收盘价标准差）

#### 成交量统计指标
- 总成交量、平均成交量
- 成交量标准差
- 最大/最小成交量

#### 强度指标
- 上边界触碰次数
- 下边界触碰次数
- 触碰比率（触碰次数/K线数量）

## 使用方法

### 命令行使用

```bash
python box_nms_processor.py \
    --scores scores.csv \
    --klines BTCUSDT_4h.json \
    --output processed_boxes.json \
    --iou-threshold 0.3 \
    --min-score 10.0
```

### 参数说明

- `--scores`: 箱体得分CSV文件路径
- `--klines`: K线数据JSON文件路径  
- `--output`: 输出JSON文件路径
- `--iou-threshold`: IoU阈值（默认0.5）
- `--min-score`: 最小得分阈值（默认0.0）

### Python代码使用

```python
from box_nms_processor import BoxNMSProcessor

# 创建处理器
processor = BoxNMSProcessor(
    iou_threshold=0.3,
    min_score=10.0
)

# 执行处理
results = processor.process(
    scores_csv_path="scores.csv",
    klines_json_path="BTCUSDT_4h.json", 
    output_path="processed_boxes.json"
)
```

## 输入数据格式

### 1. scores.csv
- CSV格式的得分矩阵
- 行号表示箱体左边界K线编号
- 列号表示箱体右边界K线编号
- 单元格值表示箱体成立得分

### 2. K线数据JSON
- JSON数组格式
- 每个元素包含：`[timestamp, open, high, low, close, volume, ...]`

## 输出数据格式

```json
{
  "metadata": {
    "total_candidates": 1000,
    "selected_boxes": 50,
    "iou_threshold": 0.3,
    "min_score": 10.0,
    "klines_count": 5000
  },
  "boxes": [
    {
      "box_id": 0,
      "boundaries": {
        "left_idx": 100,
        "right_idx": 150,
        "min_price": 64000.0,
        "max_price": 65000.0
      },
      "score": 25.5,
      "technical_indicators": {
        "basic_stats": {
          "duration_candles": 51,
          "duration_hours": 204,
          "price_range": 1000.0,
          "price_center": 64500.0,
          "area": 51000.0
        },
        "price_stats": {
          "min_price": 64000.0,
          "max_price": 65000.0,
          "open_price": 64200.0,
          "close_price": 64800.0,
          "price_change": 600.0,
          "price_change_pct": 0.93,
          "price_volatility": 250.5
        },
        "volume_stats": {
          "total_volume": 1000000.0,
          "avg_volume": 19607.8,
          "volume_std": 5000.0,
          "max_volume": 30000.0,
          "min_volume": 10000.0
        },
        "strength_indicators": {
          "upper_touches": 5,
          "lower_touches": 3,
          "touch_ratio": 0.157
        }
      }
    }
  ]
}
```

## 参数调优建议

### IoU阈值设置
- **0.2-0.3**: 保守设置，输出较少但质量高的箱体
- **0.3-0.4**: 平衡设置，推荐用于大多数场景
- **0.4-0.6**: 激进设置，保留更多可能重叠的箱体

### 最小得分阈值
- 根据scores.csv中的得分分布来设置
- 建议先查看得分分布，然后设置合适的阈值过滤低质量候选

## 算法特点

### 1. 智能重叠处理
- 传统NMS只考虑IoU阈值
- 本算法额外考虑包含关系，避免误删有效的大小箱体组合

### 2. 二维IoU计算
- 同时考虑时间维度和价格维度的重叠
- 更准确地反映箱体在K线图上的实际重叠情况

### 3. 丰富的技术指标
- 提供多维度的箱体分析指标
- 便于后续的箱体质量评估和策略开发

## 示例和测试

运行示例程序：
```bash
python tmp/box_nms_example.py
```

这将使用默认参数处理数据并输出详细的分析结果。

## 注意事项

1. 确保输入文件路径正确且文件存在
2. CSV文件中的空单元格会被处理为0分
3. K线索引超出范围的箱体会被跳过
4. 建议根据实际数据调整IoU阈值和最小得分阈值
